<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>STX分隔符测试</title>
</head>
<body>
    <h1>STX分隔符测试</h1>
    <button onclick="testSTX()">测试STX分隔符</button>
    <div id="result"></div>

    <script>
        function testSTX() {
            const numbers = ['GL-1', 'GL-2', 'GL-3', 'GL-4'];
            const stxChar = String.fromCharCode(2); // STX字符
            const result = numbers.join(stxChar);
            
            // 显示结果
            let display = '';
            for (let i = 0; i < result.length; i++) {
                const char = result[i];
                const code = result.charCodeAt(i);
                if (code === 2) {
                    display += '[STX]';
                } else {
                    display += char;
                }
            }
            
            document.getElementById('result').innerHTML = `
                <p>原始编号: ${numbers.join(', ')}</p>
                <p>STX分隔结果: ${display}</p>
                <p>字节长度: ${result.length}</p>
            `;
            
            console.log('STX分隔的字符串:', result);
            console.log('字符码数组:', Array.from(result).map(c => c.charCodeAt(0)));
        }
    </script>
</body>
</html>
