<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tekla Structures 过滤器生成器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-section {
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #34495e;
        }
        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, textarea:focus, select:focus {
            border-color: #3498db;
            outline: none;
        }
        textarea {
            height: 120px;
            resize: vertical;
            font-family: monospace;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .download-btn {
            background-color: #27ae60;
        }
        .download-btn:hover {
            background-color: #229954;
        }
        .clear-btn {
            background-color: #e74c3c;
        }
        .clear-btn:hover {
            background-color: #c0392b;
        }
        .output-section {
            margin-top: 30px;
        }
        .preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .help {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        .help h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .example {
            background-color: #f1f8ff;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tekla Structures 过滤器生成器</h1>
        
        <div class="help">
            <h3>使用说明</h3>
            <p>1. 选择对象类型（构件或零件）</p>
            <p>2. 在输入框中输入位置编号，支持多种分隔符（空格、逗号、换行等）</p>
            <p>3. 点击"生成过滤器"按钮</p>
            <p>4. 点击"下载.VObjGrp文件"将文件保存到本地</p>
            <p>5. 将生成的文件复制到Tekla Structures中使用，突破长度限制</p>
            <p><strong>注意：</strong>生成的文件中编号间使用STX分隔符（ASCII码2），这是Tekla要求的格式</p>
            <div class="example">示例输入: GL-1  GL-2  GL-3  GL-4  GL-6  GL-7  GL-8  GL-9</div>
        </div>

        <div class="input-section">
            <label for="objectType">对象类型:</label>
            <select id="objectType" onchange="updateFilterName()">
                <option value="assembly">构件 (Assembly)</option>
                <option value="part">零件 (Part)</option>
            </select>
        </div>

        <div class="input-section">
            <label for="filterName">过滤器名称:</label>
            <input type="text" id="filterName" placeholder="输入过滤器名称" value="构件长度限制过滤">
        </div>

        <div class="input-section">
            <label for="partNumbers">位置编号 (支持空格、逗号、换行等分隔符):</label>
            <textarea id="partNumbers" placeholder="输入位置编号，例如：GL-1  GL-2  GL-3  GL-4  GL-6  GL-7  GL-8  GL-9"></textarea>
        </div>

        <div class="button-group">
            <button onclick="generateFilter()">生成过滤器</button>
            <button onclick="downloadFile()" class="download-btn" id="downloadBtn" disabled>下载.VObjGrp文件</button>
            <button onclick="clearAll()" class="clear-btn">清空</button>
        </div>

        <div class="output-section">
            <label>生成的过滤器内容预览:</label>
            <div class="preview" id="preview"></div>
        </div>
    </div>

    <script>
        let generatedContent = '';

        function generateFilter() {
            const filterName = document.getElementById('filterName').value.trim();
            const partNumbers = document.getElementById('partNumbers').value.trim();
            const objectType = document.getElementById('objectType').value;

            if (!filterName) {
                alert('请输入过滤器名称');
                return;
            }

            if (!partNumbers) {
                alert('请输入编号');
                return;
            }

            // 解析输入的编号，支持多种分隔符
            const numbers = partNumbers
                .split(/[\s,，\n\r\t]+/)  // 支持空格、逗号、换行、制表符等分隔符
                .filter(num => num.trim() !== '')  // 过滤空字符串
                .map(num => num.trim());  // 去除首尾空格

            if (numbers.length === 0) {
                alert('未检测到有效的编号');
                return;
            }

            // 生成用STX字符分隔的编号字符串
            const stxChar = String.fromCharCode(2); // STX字符 (ASCII码2)
            const concatenatedNumbers = numbers.join(stxChar);

            // 根据对象类型设置不同的参数
            let objectClass, positionProperty;
            if (objectType === 'assembly') {
                objectClass = 'co_assembly';
                positionProperty = 'proASSEMBLY_POSITION';
            } else {
                objectClass = 'co_part';
                positionProperty = 'proNUMBERING_POSITION';
            }

            // 生成.VObjGrp文件内容
            generatedContent = `TITLE_OBJECT_GROUP
{
    Version= 1.05
    Count= 1
    SECTION_OBJECT_GROUP
    {
        0
        1
        ${objectClass}
        ${positionProperty}
        albl_Position_number
        ==
        albl_Equals
        ${concatenatedNumbers}
        0
        &&
        }
    }
`;

            // 显示预览
            document.getElementById('preview').textContent = generatedContent;

            // 启用下载按钮
            document.getElementById('downloadBtn').disabled = false;

            // 静默生成，不显示弹窗
        }

        function downloadFile() {
            if (!generatedContent) {
                alert('请先生成过滤器内容');
                return;
            }

            const filterName = document.getElementById('filterName').value.trim();
            const filename = `${filterName}.VObjGrp`;
            
            // 创建Blob对象
            const blob = new Blob([generatedContent], { type: 'text/plain;charset=utf-8' });
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            URL.revokeObjectURL(link.href);
        }

        function updateFilterName() {
            const objectType = document.getElementById('objectType').value;
            const filterNameInput = document.getElementById('filterName');

            if (objectType === 'assembly') {
                filterNameInput.value = '构件长度限制过滤';
            } else {
                filterNameInput.value = '零件长度限制过滤';
            }
        }

        function clearAll() {
            document.getElementById('objectType').value = 'assembly';
            updateFilterName();
            document.getElementById('partNumbers').value = '';
            document.getElementById('preview').textContent = '';
            document.getElementById('downloadBtn').disabled = true;
            generatedContent = '';
        }

        // 示例数据填充功能
        function fillExample() {
            document.getElementById('partNumbers').value = 'GL-1  GL-2  GL-3  GL-4  GL-6  GL-7  GL-8  GL-9';
        }

        // 页面加载时添加示例按钮
        window.onload = function() {
            const buttonGroup = document.querySelector('.button-group');
            const exampleBtn = document.createElement('button');
            exampleBtn.textContent = '填充示例';
            exampleBtn.style.backgroundColor = '#f39c12';
            exampleBtn.onclick = fillExample;
            exampleBtn.onmouseover = function() { this.style.backgroundColor = '#e67e22'; };
            exampleBtn.onmouseout = function() { this.style.backgroundColor = '#f39c12'; };
            buttonGroup.appendChild(exampleBtn);
        };
    </script>
</body>
</html>
